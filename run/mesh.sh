#!/bin/bash

# TIMR 场景Mesh测试脚本
# 支持指定场景生成BSP mesh

set -e  # 遇到错误立即退出

# ============================================================================
# 🔧 配置区域 - 在这里修改你的配置
# ============================================================================

# 实验目录 (修改为你的实验目录)
EXP_DIR="experiments/timr_20250728_144113"

# 模型文件名 (相对于实验目录，或者填写完整路径)
MODEL_FILE="best_model.pth"  # 也可以是 "epoch_100.pth" 等

# 测试场景 (修改为你要测试的场景)
SCENE_NAME="scene0030_02"

# 配置文件
CONFIG_FILE="configs/timr_scannet.yaml"

# GPU设备ID
GPU_ID=0

# Mesh生成参数
CONF_THRESHOLD=0.1  # 置信度阈值
ENABLE_DEDUP=false  # 是否启用去重 (默认关闭)
DEDUP_IOU_THRESHOLD=0.3  # 去重IoU阈值

# 是否保存mask
SAVE_MASKS=false

# ============================================================================
# 🚀 脚本逻辑 - 一般不需要修改
# ============================================================================

echo "🎨 TIMR 场景Mesh生成"
echo "================================"

# 解析命令行参数覆盖默认配置
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--experiment)
            EXP_DIR="$2"
            shift 2
            ;;
        -m|--model)
            MODEL_FILE="$2"
            shift 2
            ;;
        -s|--scene)
            SCENE_NAME="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -g|--gpu)
            GPU_ID="$2"
            shift 2
            ;;
        --conf-threshold)
            CONF_THRESHOLD="$2"
            shift 2
            ;;
        --enable-dedup)
            ENABLE_DEDUP=true
            shift
            ;;
        --disable-dedup|--no-dedup)
            ENABLE_DEDUP=false
            shift
            ;;
        --dedup-iou-threshold)
            DEDUP_IOU_THRESHOLD="$2"
            shift 2
            ;;
        --no-masks)
            SAVE_MASKS=false
            shift
            ;;
        -h|--help)
            cat << EOF
🎨 TIMR 场景Mesh生成脚本

配置 (在脚本顶部修改):
    EXP_DIR="experiments/timr_20250614_154729"
    MODEL_FILE="best_model.pth"
    SCENE_NAME="scene0011_00"

命令行选项 (覆盖默认配置):
    -e, --experiment PATH       实验目录路径
    -m, --model PATH            模型文件名或路径
    -s, --scene NAME            场景名称
    -c, --config PATH           配置文件路径
    -g, --gpu ID                GPU设备ID
    --conf-threshold VALUE      置信度阈值 (默认: 0.3)
    --enable-dedup              启用去重
    --disable-dedup, --no-dedup 禁用去重
    --dedup-iou-threshold VALUE 去重IoU阈值 (默认: 0.3)
    --no-masks                  不保存mask
    -h, --help                  显示帮助信息

示例:
    $0                          # 使用脚本顶部配置
    $0 -s scene0000_00          # 测试指定场景
    $0 -e experiments/my_exp    # 使用指定实验
EOF
            exit 0
            ;;
        *)
            echo "❌ 未知参数: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo "🔍 检查环境和依赖..."

# 检查Python依赖
python -c "import torch; print('✅ PyTorch 版本:', torch.__version__)" || {
    echo "❌ PyTorch未安装"
    exit 1
}

python -c "import yaml; print('✅ PyYAML已安装')" || {
    echo "❌ PyYAML未安装"
    exit 1
}

# 检查CUDA
if command -v nvidia-smi > /dev/null; then
    echo "✅ CUDA可用 (GPU $GPU_ID)"
    nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits | sed -n "$((GPU_ID+1))p" || echo "⚠️  GPU $GPU_ID 信息获取失败"
else
    echo "⚠️  CUDA不可用，将使用CPU"
fi

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$GPU_ID
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 构建完整路径
if [[ "$MODEL_FILE" == /* ]]; then
    # 绝对路径
    MODEL_PATH="$MODEL_FILE"
else
    # 相对路径，相对于实验目录
    MODEL_PATH="$EXP_DIR/$MODEL_FILE"
fi

# 设置输出目录
OUTPUT_DIR="$EXP_DIR/output"

# 验证文件和目录
echo "📋 验证配置..."
[ -f "$CONFIG_FILE" ] || { echo "❌ 配置文件不存在: $CONFIG_FILE"; exit 1; }
[ -d "$EXP_DIR" ] || { echo "❌ 实验目录不存在: $EXP_DIR"; exit 1; }
[ -f "$MODEL_PATH" ] || { echo "❌ 模型文件不存在: $MODEL_PATH"; exit 1; }

echo "✅ 配置验证通过"
echo "   实验目录: $EXP_DIR"
echo "   模型文件: $MODEL_PATH"
echo "   测试场景: $SCENE_NAME"
echo "   配置文件: $CONFIG_FILE"
echo "   输出目录: $OUTPUT_DIR"
echo "   GPU设备: $GPU_ID"
echo "   置信度阈值: $CONF_THRESHOLD"
echo "   启用去重: $ENABLE_DEDUP"
echo "   去重IoU阈值: $DEDUP_IOU_THRESHOLD"
echo "   保存mask: $SAVE_MASKS"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 构建测试命令
CMD="python test_scene_mesh.py \"$CONFIG_FILE\" \"$MODEL_PATH\""
CMD="$CMD --scene \"$SCENE_NAME\""
CMD="$CMD --out \"$OUTPUT_DIR\""
CMD="$CMD --gpu $GPU_ID"
CMD="$CMD --conf-threshold $CONF_THRESHOLD"

# 添加去重参数
if [ "$ENABLE_DEDUP" = true ]; then
    CMD="$CMD --enable-dedup"
    CMD="$CMD --dedup-iou-threshold $DEDUP_IOU_THRESHOLD"
else
    CMD="$CMD --disable-dedup"
fi

# 添加mask保存参数
if [ "$SAVE_MASKS" = true ]; then
    CMD="$CMD --save-masks"
fi

echo ""
echo "🎯 开始生成场景mesh..."
echo "================================"
echo "执行命令: $CMD"
echo "================================"

# 记录开始时间
START_TIME=$(date +%s)

# 执行测试
eval $CMD

# 记录结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "================================"
echo "🎉 Mesh生成完成!"
echo "⏱️  耗时: ${DURATION}秒"

# 显示结果统计
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 结果统计:"
    
    # 检查mesh文件
    if [ -d "$OUTPUT_DIR/meshes/$SCENE_NAME" ]; then
        MESH_COUNT=$(find "$OUTPUT_DIR/meshes/$SCENE_NAME" -name "*.ply" | wc -l)
        echo "   生成mesh数量: $MESH_COUNT"
        echo "   Mesh保存位置: $OUTPUT_DIR/meshes/$SCENE_NAME"
        
        if [ $MESH_COUNT -gt 0 ]; then
            echo "   Mesh文件列表:"
            find "$OUTPUT_DIR/meshes/$SCENE_NAME" -name "*.ply" | head -10 | sed 's/^/     /'
            if [ $MESH_COUNT -gt 10 ]; then
                echo "     ... 还有 $((MESH_COUNT-10)) 个文件"
            fi
        fi
    else
        echo "   ❌ 未找到mesh文件"
    fi
    
    # 检查场景详细结果
    if [ -d "$OUTPUT_DIR/scenes/$SCENE_NAME" ]; then
        echo "   场景详细结果: $OUTPUT_DIR/scenes/$SCENE_NAME"
        if [ -f "$OUTPUT_DIR/scenes/$SCENE_NAME/${SCENE_NAME}_instances.txt" ]; then
            INSTANCE_COUNT=$(grep -v "^#" "$OUTPUT_DIR/scenes/$SCENE_NAME/${SCENE_NAME}_instances.txt" | wc -l)
            echo "   检测实例数量: $INSTANCE_COUNT"
        fi
    fi
    
    # 检查总结文件
    if [ -f "$OUTPUT_DIR/test_summary.txt" ]; then
        echo "   测试总结: $OUTPUT_DIR/test_summary.txt"
        echo "   总结内容:"
        cat "$OUTPUT_DIR/test_summary.txt" | grep -E "(Scene|Confidence|Deduplication|Total meshes)" | sed 's/^/     /'
    fi
fi

echo ""
echo "📁 输出目录结构:"
if [ -d "$OUTPUT_DIR" ]; then
    echo "   $OUTPUT_DIR/"
    [ -d "$OUTPUT_DIR/meshes" ] && echo "   ├── meshes/"
    [ -d "$OUTPUT_DIR/meshes/$SCENE_NAME" ] && echo "   │   └── $SCENE_NAME/ (*.ply文件)"
    [ -d "$OUTPUT_DIR/scenes" ] && echo "   ├── scenes/"
    [ -d "$OUTPUT_DIR/scenes/$SCENE_NAME" ] && echo "   │   └── $SCENE_NAME/ (实例信息)"
    [ -f "$OUTPUT_DIR/test_summary.txt" ] && echo "   └── test_summary.txt"
fi

echo ""
echo "💡 使用提示:"
echo "   • Mesh文件可用CloudCompare、MeshLab等软件查看"
echo "   • 修改脚本顶部配置可快速切换实验/场景"
echo "   • 使用 $0 -h 查看更多选项"
echo ""
echo "✨ 测试流程结束"